using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace RazeWinComTr.Tests.Optimization
{
    /// <summary>
    /// Tests to verify that zero reward amounts don't trigger unnecessary database operations
    /// </summary>
    public class ZeroRewardOptimizationTests
    {
        private DbContextOptions<AppDbContext> GetInMemoryDbOptions()
        {
            return new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
        }

        /// <summary>
        /// Tests that when RZW reward is 0, wallet service is not called
        /// </summary>
        [Fact]
        public async Task ProcessDepositRewardsAsync_WithZeroRzwReward_DoesNotCallWalletService()
        {
            // Arrange
            var options = GetInMemoryDbOptions();
            await using var dbContext = new AppDbContext(options);

            // Create test data with 0% RZW percentage
            var depositUser = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                Name = "Deposit",
                Surname = "User",
                ReferrerId = 2,
                Balance = 0,
                CrDate = DateTime.UtcNow,
                IdentityNumber = "12345678901",
                PhoneNumber = "5551234567",
                BirthDate = DateTime.UtcNow.AddYears(-25),
                IsActive = 1,
                ReferralCode = "DEP001"
            };

            var referrerUser = new User
            {
                UserId = 2,
                Email = "<EMAIL>",
                Name = "Referrer",
                Surname = "User",
                Balance = 0,
                CrDate = DateTime.UtcNow,
                IdentityNumber = "12345678902",
                PhoneNumber = "5551234568",
                BirthDate = DateTime.UtcNow.AddYears(-30),
                IsActive = 1,
                ReferralCode = "REF001"
            };

            var package = new Package
            {
                Id = 1,
                Name = "Test Package",
                Price = 100,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            var packagePercentage = new PackageRewardPercentage
            {
                Id = 1,
                PackageId = 1,
                Level = 1,
                RzwPercentage = 0, // 0% RZW reward
                TlPercentage = 10, // 10% TL reward
                CreatedDate = DateTime.UtcNow
            };

            var userPackage = new UserPackage
            {
                Id = 1,
                UserId = 2,
                PackageId = 1,
                Status = UserPackageStatus.Active,
                PurchaseDate = DateTime.UtcNow.AddDays(-1),
                ExpiryDate = DateTime.UtcNow.AddDays(30),
                CreatedDate = DateTime.UtcNow
            };

            var deposit = new Deposit
            {
                Id = 1,
                UserId = 1,
                Amount = 1000,
                Status = DepositStatus.Approved,
                RewardStatus = DepositRewardStatus.Pending,
                CreatedDate = DateTime.UtcNow
            };

            dbContext.Users.AddRange(depositUser, referrerUser);
            dbContext.Packages.Add(package);
            dbContext.PackageRewardPercentages.Add(packagePercentage);
            dbContext.UserPackages.Add(userPackage);
            dbContext.Deposits.Add(deposit);
            await dbContext.SaveChangesAsync();

            // Mock services
            var mockWalletService = new Mock<IWalletService>();
            var mockTokenPriceService = new Mock<ITokenPriceService>();
            var mockLogger = new Mock<ILogger<ReferralRewardService>>();
            var mockTradeService = new Mock<ITradeService>();

            // Setup token price service
            mockTokenPriceService.Setup(x => x.GetCurrentRzwBuyPriceAsync())
                .ReturnsAsync(2.0m);

            mockTokenPriceService.Setup(x => x.GetRzwTokenInfoAsync())
                .ReturnsAsync(new RzwTokenInfo { TokenId = 1, BuyPrice = 2.0m, SellPrice = 1.8m });

            // Create service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(1, result.RewardedUsersCount);
            Assert.Equal(0m, result.TotalRzwDistributed); // RZW reward should be 0
            Assert.Equal(100m, result.TotalTlDistributed); // TL reward should be 100 (1000 * 10%)

            // Verify wallet service was NOT called for RZW (since amount is 0)
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.IsAny<int>(),
                It.IsAny<RzwTokenInfo>(),
                It.IsAny<decimal>(),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Never);

            // Verify trade service was NOT called (since RZW amount is 0)
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Never);

            // Verify TL balance transaction was created
            var balanceTransactions = await dbContext.BalanceTransactions.ToListAsync();
            Assert.Single(balanceTransactions);
            Assert.Equal(100m, balanceTransactions[0].Amount);
        }

        /// <summary>
        /// Tests that when TL reward is 0, balance transaction is not created
        /// </summary>
        [Fact]
        public async Task ProcessDepositRewardsAsync_WithZeroTlReward_DoesNotCreateBalanceTransaction()
        {
            // Arrange
            var options = GetInMemoryDbOptions();
            await using var dbContext = new AppDbContext(options);

            // Create test data with 0% TL percentage
            var depositUser = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                Name = "Deposit",
                Surname = "User",
                ReferrerId = 2,
                Balance = 0,
                CrDate = DateTime.UtcNow,
                IdentityNumber = "12345678901",
                PhoneNumber = "5551234567",
                BirthDate = DateTime.UtcNow.AddYears(-25),
                IsActive = 1,
                ReferralCode = "DEP001"
            };

            var referrerUser = new User
            {
                UserId = 2,
                Email = "<EMAIL>",
                Name = "Referrer",
                Surname = "User",
                Balance = 0,
                CrDate = DateTime.UtcNow,
                IdentityNumber = "12345678902",
                PhoneNumber = "5551234568",
                BirthDate = DateTime.UtcNow.AddYears(-30),
                IsActive = 1,
                ReferralCode = "REF001"
            };

            var package = new Package
            {
                Id = 1,
                Name = "Test Package",
                Price = 100,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            var packagePercentage = new PackageRewardPercentage
            {
                Id = 1,
                PackageId = 1,
                Level = 1,
                RzwPercentage = 10, // 10% RZW reward
                TlPercentage = 0, // 0% TL reward
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            var userPackage = new UserPackage
            {
                Id = 1,
                UserId = 2,
                PackageId = 1,
                IsActive = true,
                StartDate = DateTime.UtcNow.AddDays(-1),
                EndDate = DateTime.UtcNow.AddDays(30),
                CreatedDate = DateTime.UtcNow
            };

            var deposit = new Deposit
            {
                Id = 1,
                UserId = 1,
                Amount = 1000,
                Status = DepositStatus.Approved,
                RewardStatus = DepositRewardStatus.Pending,
                CreatedDate = DateTime.UtcNow
            };

            dbContext.Users.AddRange(depositUser, referrerUser);
            dbContext.Packages.Add(package);
            dbContext.PackageRewardPercentages.Add(packagePercentage);
            dbContext.UserPackages.Add(userPackage);
            dbContext.Deposits.Add(deposit);
            await dbContext.SaveChangesAsync();

            // Mock services
            var mockWalletService = new Mock<IWalletService>();
            var mockTokenPriceService = new Mock<ITokenPriceService>();
            var mockLogger = new Mock<ILogger<ReferralRewardService>>();
            var mockTradeService = new Mock<ITradeService>();

            // Setup token price service
            mockTokenPriceService.Setup(x => x.GetRzwBuyPriceAsync())
                .ReturnsAsync(2.0m);

            mockTokenPriceService.Setup(x => x.GetRzwTokenInfoAsync())
                .ReturnsAsync(new RzwTokenInfo { TokenId = 1, Symbol = "RZW" });

            // Setup wallet service to return a wallet
            var wallet = new Wallet
            {
                UserId = 2,
                CoinId = 1,
                Balance = 50m,
                CreatedDate = DateTime.UtcNow
            };
            mockWalletService.Setup(x => x.GetUserAvailableBalanceAsync(2, 1))
                .ReturnsAsync(0m);
            mockWalletService.Setup(x => x.AddAvailableBalanceAsync(
                It.IsAny<int>(),
                It.IsAny<RzwTokenInfo>(),
                It.IsAny<decimal>(),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()))
                .ReturnsAsync(wallet);

            // Create service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(1, result.RewardedUsersCount);
            Assert.Equal(50m, result.TotalRzwDistributed); // RZW reward should be 50 (1000 * 10% / 2.0)
            Assert.Equal(0m, result.TotalTlDistributed); // TL reward should be 0

            // Verify wallet service WAS called for RZW (since amount > 0)
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                2,
                It.IsAny<RzwTokenInfo>(),
                50m,
                TradeType.ReferralReward,
                It.IsAny<AppDbContext>()), Times.Once);

            // Verify trade service WAS called (since RZW amount > 0)
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Once);

            // Verify NO balance transaction was created (since TL amount is 0)
            var balanceTransactions = await dbContext.BalanceTransactions.ToListAsync();
            Assert.Empty(balanceTransactions);

            // Verify user's TL balance was NOT updated (since TL reward is 0)
            var updatedUser = await dbContext.Users.FindAsync(2);
            Assert.Equal(0m, updatedUser.Balance);
        }
    }
}
